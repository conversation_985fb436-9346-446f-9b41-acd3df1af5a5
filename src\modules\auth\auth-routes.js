import { AuthService } from './auth-service.js';
import { 
  registerSchema, 
  loginSchema, 
  forgotPasswordSchema, 
  resetPasswordSchema 
} from '../../core/utils/validation.js';
import { authenticate } from '../../core/middleware/auth.js';

/**
 * Auth routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function authRoutes(fastify, options) {
  const authService = new AuthService(fastify.db, fastify.emailService);

  // Register endpoint
  fastify.post('/auth/register', {
    schema: {
      body: {
        type: 'object',
        required: ['username', 'email', 'password', 'firstName', 'lastName'],
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 50 },
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          firstName: { type: 'string', minLength: 1, maxLength: 100 },
          lastName: { type: 'string', minLength: 1, maxLength: 100 },
        },
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: { type: 'object' },
                token: { type: 'string' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    // Validate request body
    const userData = registerSchema.parse(request.body);
    
    // Register user
    const user = await authService.register(userData);
    
    // Generate JWT token
    const token = fastify.jwt.sign({ userId: user.id });
    
    return reply.status(201).send({
      success: true,
      data: {
        user,
        token,
      },
    });
  });

  // Login endpoint
  fastify.post('/auth/login', {
    schema: {
      body: {
        type: 'object',
        required: ['login', 'password'],
        properties: {
          login: { type: 'string', minLength: 1 },
          password: { type: 'string', minLength: 1 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: { type: 'object' },
                token: { type: 'string' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    // Validate request body
    const loginData = loginSchema.parse(request.body);
    
    // Login user
    const user = await authService.login(loginData);
    
    // Generate JWT token
    const token = fastify.jwt.sign({ userId: user.id });
    
    return reply.send({
      success: true,
      data: {
        user,
        token,
      },
    });
  });

  // Forgot password endpoint
  fastify.post('/auth/forgot-password', {
    schema: {
      body: {
        type: 'object',
        required: ['email'],
        properties: {
          email: { type: 'string', format: 'email' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    // Validate request body
    const { email } = forgotPasswordSchema.parse(request.body);
    
    // Generate reset token and send email
    await authService.forgotPassword(email);
    
    return reply.send({
      success: true,
      message: 'Password reset email sent successfully',
    });
  });

  // Reset password endpoint
  fastify.post('/auth/reset-password', {
    schema: {
      body: {
        type: 'object',
        required: ['token', 'password'],
        properties: {
          token: { type: 'string', minLength: 1 },
          password: { type: 'string', minLength: 8 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: { type: 'object' },
                token: { type: 'string' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    // Validate request body
    const resetData = resetPasswordSchema.parse(request.body);
    
    // Reset password
    const user = await authService.resetPassword(resetData);
    
    // Generate new JWT token
    const token = fastify.jwt.sign({ userId: user.id });
    
    return reply.send({
      success: true,
      data: {
        user,
        token,
      },
    });
  });

  // Logout endpoint
  fastify.post('/auth/logout', {
    preHandler: authenticate,
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    // In a stateless JWT system, logout is handled client-side
    // The client should remove the token from storage
    // For more security, you could implement a token blacklist
    
    return reply.send({
      success: true,
      message: 'Logged out successfully',
    });
  });
}
