import { PostService } from './post-service.js';
import {
  createPostSchema,
  updatePostSchema,
  getPostsSchema,
  idSchema
} from '../../core/utils/validation.js';
import { authenticate, optionalAuthenticate } from '../../core/middleware/auth.js';
import { NotFoundError } from '../../core/utils/errors.js';

/**
 * Post routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function postRoutes(fastify, options) {
  const postService = new PostService(fastify.db);

  // Get all posts with pagination and search
  fastify.get('/posts', {
    preHandler: optionalAuthenticate,
    schema: {
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', pattern: '^[1-9][0-9]*$' },
          limit: { type: 'string', pattern: '^[1-9][0-9]*$' },
          search: { type: 'string', maxLength: 100 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                posts: { type: 'array' },
                pagination: {
                  type: 'object',
                  properties: {
                    currentPage: { type: 'number' },
                    totalPages: { type: 'number' },
                    totalItems: { type: 'number' },
                    itemsPerPage: { type: 'number' },
                    hasNextPage: { type: 'boolean' },
                    hasPreviousPage: { type: 'boolean' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    // Validate query parameters
    const queryParams = getPostsSchema.parse(request.query);
    
    // Get posts
    const result = await postService.getPosts(queryParams);
    
    return reply.send({
      success: true,
      data: result,
    });
  });

  // Get post by ID
  fastify.get('/posts/:id', {
    preHandler: optionalAuthenticate,
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', minLength: 1 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                post: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { id } = request.params;
    
    // Validate post ID
    idSchema.parse(id);
    
    // Get post
    const post = await postService.getPostById(id);
    
    // Check if post is published or user owns it
    if (!post.isPublished && (!request.currentUser || request.currentUser.id !== post.authorId)) {
      throw new NotFoundError('Post not found');
    }
    
    return reply.send({
      success: true,
      data: {
        post,
      },
    });
  });

  // Create new post
  fastify.post('/posts', {
    preHandler: authenticate,
    schema: {
      body: {
        type: 'object',
        required: ['title', 'content'],
        properties: {
          title: { type: 'string', minLength: 1, maxLength: 255 },
          content: { type: 'string', minLength: 1 },
          excerpt: { type: 'string', maxLength: 500 },
          isPublished: { type: 'boolean' },
        },
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                post: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const userId = request.currentUser.id;
    
    // Validate request body
    const postData = createPostSchema.parse(request.body);
    
    // Create post
    const post = await postService.createPost(postData, userId);
    
    return reply.status(201).send({
      success: true,
      data: {
        post,
      },
    });
  });

  // Update post
  fastify.put('/posts/:id', {
    preHandler: authenticate,
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', minLength: 1 },
        },
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', minLength: 1, maxLength: 255 },
          content: { type: 'string', minLength: 1 },
          excerpt: { type: 'string', maxLength: 500 },
          isPublished: { type: 'boolean' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                post: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { id } = request.params;
    const userId = request.currentUser.id;
    
    // Validate post ID and request body
    idSchema.parse(id);
    const updateData = updatePostSchema.parse(request.body);
    
    // Update post
    const post = await postService.updatePost(id, updateData, userId);
    
    return reply.send({
      success: true,
      data: {
        post,
      },
    });
  });

  // Delete post
  fastify.delete('/posts/:id', {
    preHandler: authenticate,
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string', minLength: 1 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { id } = request.params;
    const userId = request.currentUser.id;
    
    // Validate post ID
    idSchema.parse(id);
    
    // Delete post
    await postService.deletePost(id, userId);
    
    return reply.send({
      success: true,
      message: 'Post deleted successfully',
    });
  });

  // Get posts by current user
  fastify.get('/posts/me', {
    preHandler: authenticate,
    schema: {
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'string', pattern: '^[1-9][0-9]*$' },
          limit: { type: 'string', pattern: '^[1-9][0-9]*$' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                posts: { type: 'array' },
                pagination: { type: 'object' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const userId = request.currentUser.id;
    const { page = 1, limit = 10 } = request.query;
    
    // Get user's posts (including unpublished)
    const result = await postService.getPostsByAuthor(userId, {
      page: parseInt(page),
      limit: parseInt(limit),
      includeUnpublished: true,
    });
    
    return reply.send({
      success: true,
      data: result,
    });
  });
}
