import { AuthenticationError, NotFoundError } from '../utils/errors.js';
import { eq } from 'drizzle-orm';
import { users } from '../database/schema.js';

/**
 * JWT Authentication middleware
 * Verifies JW<PERSON> token and attaches user to request
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 */
export async function authenticate(request, reply) {
  try {
    // Verify JWT token (handled by @fastify/jwt)
    await request.jwtVerify();
    
    // Get user from database
    const db = request.server.db;
    const [user] = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        bio: users.bio,
        avatar: users.avatar,
        isEmailVerified: users.isEmailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.id, request.user.userId));

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Attach user to request
    request.currentUser = user;
  } catch (error) {
    if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER' || 
        error.code === 'FST_JWT_AUTHORIZATION_TOKEN_INVALID') {
      throw new AuthenticationError('Invalid or missing token');
    }
    throw error;
  }
}

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't throw error if missing
 * @param {Object} request - Fastify request object
 * @param {Object} reply - Fastify reply object
 */
export async function optionalAuthenticate(request, reply) {
  try {
    await authenticate(request, reply);
  } catch (error) {
    // Ignore authentication errors for optional auth
    if (error instanceof AuthenticationError) {
      request.currentUser = null;
      return;
    }
    throw error;
  }
}

/**
 * Check if current user owns the resource
 * @param {string} resourceUserId - The user ID of the resource owner
 * @param {Object} currentUser - The current authenticated user
 * @throws {AuthorizationError} If user doesn't own the resource
 */
export function checkOwnership(resourceUserId, currentUser) {
  if (!currentUser || currentUser.id !== resourceUserId) {
    throw new AuthorizationError('You can only access your own resources');
  }
}
