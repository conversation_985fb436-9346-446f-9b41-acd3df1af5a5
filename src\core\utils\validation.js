import { z } from 'zod';

// Common validation schemas
export const idSchema = z.string().min(1, 'ID is required');

export const paginationSchema = z.object({
  page: z.string().transform(Number).pipe(z.number().int().min(1)).default('1'),
  limit: z.string().transform(Number).pipe(z.number().int().min(1).max(100)).default('10'),
});

export const searchSchema = z.object({
  search: z.string().optional(),
});

// Auth validation schemas
export const registerSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: z.string().email('Invalid email format'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  firstName: z.string().min(1, 'First name is required').max(100),
  lastName: z.string().min(1, 'Last name is required').max(100),
});

export const loginSchema = z.object({
  login: z.string().min(1, 'Username or email is required'), // Can be username or email
  password: z.string().min(1, 'Password is required'),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email format'),
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
});

// User validation schemas
export const updateUserSchema = z.object({
  firstName: z.string().min(1).max(100).optional(),
  lastName: z.string().min(1).max(100).optional(),
  bio: z.string().max(1000).optional(),
  avatar: z.string().url().optional(),
});

// Post validation schemas
export const createPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  content: z.string().min(1, 'Content is required'),
  excerpt: z.string().max(500).optional(),
  isPublished: z.boolean().default(false),
});

export const updatePostSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  content: z.string().min(1).optional(),
  excerpt: z.string().max(500).optional(),
  isPublished: z.boolean().optional(),
});

export const getPostsSchema = z.object({
  ...paginationSchema.shape,
  ...searchSchema.shape,
});

/**
 * Create Fastify schema from Zod schema
 * @param {Object} schemas - Object containing body, querystring, params schemas
 * @returns {Object} Fastify schema object
 */
export function createFastifySchema(schemas = {}) {
  const fastifySchema = {};

  if (schemas.body) {
    fastifySchema.body = zodToJsonSchema(schemas.body);
  }

  if (schemas.querystring) {
    fastifySchema.querystring = zodToJsonSchema(schemas.querystring);
  }

  if (schemas.params) {
    fastifySchema.params = zodToJsonSchema(schemas.params);
  }

  return fastifySchema;
}

/**
 * Convert Zod schema to JSON Schema (simplified version)
 * @param {ZodSchema} zodSchema - Zod schema
 * @returns {Object} JSON Schema object
 */
function zodToJsonSchema(zodSchema) {
  // This is a simplified implementation
  // In production, you might want to use a library like zod-to-json-schema
  return { type: 'object' };
}
