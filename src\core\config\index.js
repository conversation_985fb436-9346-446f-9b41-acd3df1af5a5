import { z } from 'zod';

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  
  // Database
  DB_HOST: z.string().default('localhost'),
  DB_PORT: z.string().transform(Number).default('3306'),
  DB_USER: z.string().default('root'),
  DB_PASSWORD: z.string().default(''),
  DB_NAME: z.string().default('fus_app_db'),
  
  // JWT
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('7d'),
  
  // Mailgun
  MAILGUN_API_KEY: z.string(),
  MAILGUN_DOMAIN: z.string(),
  MAILGUN_FROM_EMAIL: z.string().email(),
  
  // App
  APP_URL: z.string().url().default('http://localhost:3000'),
  RESET_PASSWORD_URL: z.string().url().default('http://localhost:3001/reset-password'),
});

/**
 * Load and validate environment configuration
 * @returns {Object} Validated configuration object
 */
export function loadConfig() {
  try {
    const config = envSchema.parse(process.env);
    
    return {
      env: config.NODE_ENV,
      port: config.PORT,
      
      database: {
        host: config.DB_HOST,
        port: config.DB_PORT,
        user: config.DB_USER,
        password: config.DB_PASSWORD,
        database: config.DB_NAME,
      },
      
      jwt: {
        secret: config.JWT_SECRET,
        expiresIn: config.JWT_EXPIRES_IN,
      },
      
      mailgun: {
        apiKey: config.MAILGUN_API_KEY,
        domain: config.MAILGUN_DOMAIN,
        fromEmail: config.MAILGUN_FROM_EMAIL,
      },
      
      app: {
        url: config.APP_URL,
        resetPasswordUrl: config.RESET_PASSWORD_URL,
      },
    };
  } catch (error) {
    console.error('Configuration validation failed:', error.errors);
    process.exit(1);
  }
}
