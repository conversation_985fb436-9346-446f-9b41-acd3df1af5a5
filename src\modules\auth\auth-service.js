import bcrypt from 'bcrypt';
import { nanoid } from 'nanoid';
import { eq, or } from 'drizzle-orm';
import { users } from '../../core/database/schema.js';
import { 
  ConflictError, 
  AuthenticationError, 
  NotFoundError, 
  ValidationError 
} from '../../core/utils/errors.js';

export class AuthService {
  constructor(db, emailService) {
    this.db = db;
    this.emailService = emailService;
  }

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Promise<Object>} Created user (without password)
   */
  async register(userData) {
    const { username, email, password, firstName, lastName } = userData;

    // Check if user already exists
    const existingUser = await this.db
      .select()
      .from(users)
      .where(or(eq(users.username, username), eq(users.email, email)))
      .limit(1);

    if (existingUser.length > 0) {
      const field = existingUser[0].username === username ? 'username' : 'email';
      throw new ConflictError(`User with this ${field} already exists`);
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);

    // Create user
    const userId = nanoid();
    await this.db.insert(users).values({
      id: userId,
      username,
      email,
      passwordHash,
      firstName,
      lastName,
    });

    // Return user without password
    const [newUser] = await this.db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        bio: users.bio,
        avatar: users.avatar,
        isEmailVerified: users.isEmailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.id, userId));

    return newUser;
  }

  /**
   * Login user
   * @param {Object} loginData - Login credentials
   * @returns {Promise<Object>} User data (without password)
   */
  async login(loginData) {
    const { login, password } = loginData;

    // Find user by username or email
    const [user] = await this.db
      .select()
      .from(users)
      .where(or(eq(users.username, login), eq(users.email, login)))
      .limit(1);

    if (!user) {
      throw new AuthenticationError('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      throw new AuthenticationError('Invalid credentials');
    }

    // Return user without password
    const { passwordHash, resetPasswordToken, resetPasswordExpires, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Generate forgot password token
   * @param {string} email - User email
   * @returns {Promise<string>} Reset token
   */
  async forgotPassword(email) {
    // Find user by email
    const [user] = await this.db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (!user) {
      throw new NotFoundError('User with this email does not exist');
    }

    // Generate reset token
    const resetToken = nanoid(32);
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    // Update user with reset token
    await this.db
      .update(users)
      .set({
        resetPasswordToken: resetToken,
        resetPasswordExpires: resetExpires,
      })
      .where(eq(users.id, user.id));

    // Send reset email
    await this.emailService.sendPasswordResetEmail(user.email, user.firstName, resetToken);

    return resetToken;
  }

  /**
   * Reset password with token
   * @param {Object} resetData - Reset password data
   * @returns {Promise<Object>} User data (without password)
   */
  async resetPassword(resetData) {
    const { token, password } = resetData;

    // Find user by reset token
    const [user] = await this.db
      .select()
      .from(users)
      .where(eq(users.resetPasswordToken, token))
      .limit(1);

    if (!user) {
      throw new ValidationError('Invalid or expired reset token');
    }

    // Check if token is expired
    if (!user.resetPasswordExpires || user.resetPasswordExpires < new Date()) {
      throw new ValidationError('Reset token has expired');
    }

    // Hash new password
    const passwordHash = await bcrypt.hash(password, 12);

    // Update user password and clear reset token
    await this.db
      .update(users)
      .set({
        passwordHash,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      })
      .where(eq(users.id, user.id));

    // Return user without password
    const { passwordHash: _, resetPasswordToken: __, resetPasswordExpires: ___, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
}
