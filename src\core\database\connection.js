import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import * as schema from './schema.js';

/**
 * Create database connection
 * @param {Object} config - Database configuration
 * @returns {Object} Drizzle database instance
 */
export function createDatabaseConnection(config) {
  const connection = mysql.createConnection({
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
    database: config.database,
  });

  return drizzle(connection, { schema, mode: 'default' });
}

/**
 * Test database connection
 * @param {Object} db - Database instance
 * @returns {Promise<boolean>} Connection status
 */
export async function testConnection(db) {
  try {
    await db.execute('SELECT 1');
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}
