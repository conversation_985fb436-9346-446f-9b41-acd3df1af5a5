import 'dotenv/config';
import { buildApp, startServer } from './src/app.js';
import { loadConfig } from './src/core/config/index.js';

// Load configuration
const config = loadConfig();

// Build application
const app = buildApp();

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  app.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    await app.close();
    app.log.info('✅ Server closed successfully');
    process.exit(0);
  } catch (error) {
    app.log.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  app.log.fatal('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  app.log.fatal('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer(app, config);
