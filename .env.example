# Environment
NODE_ENV=development

# Server
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_database_password
DB_NAME=fus_app_db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_at_least_32_characters_long
JWT_EXPIRES_IN=7d

# Mailgun Configuration
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=your_mailgun_domain
MAILGUN_FROM_EMAIL=<EMAIL>

# Application URLs
APP_URL=http://localhost:3000
RESET_PASSWORD_URL=http://localhost:3001/reset-password

# Frontend URL (for CORS and email links)
FRONTEND_URL=http://localhost:3001
