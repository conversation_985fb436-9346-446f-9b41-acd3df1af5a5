import { UserService } from './user-service.js';
import { updateUserSchema, idSchema } from '../../core/utils/validation.js';
import { authenticate } from '../../core/middleware/auth.js';

/**
 * User routes plugin
 * @param {Object} fastify - Fastify instance
 * @param {Object} options - Plugin options
 */
export async function userRoutes(fastify, options) {
  const userService = new UserService(fastify.db);

  // Get user by username (public profile)
  fastify.get('/users/:username', {
    schema: {
      params: {
        type: 'object',
        required: ['username'],
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 50 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    username: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    bio: { type: 'string' },
                    avatar: { type: 'string' },
                    createdAt: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { username } = request.params;
    
    // Validate username parameter
    idSchema.parse(username);
    
    // Get user profile
    const user = await userService.getUserByUsername(username);
    
    return reply.send({
      success: true,
      data: {
        user,
      },
    });
  });

  // Get current user profile (private)
  fastify.get('/users/me', {
    preHandler: authenticate,
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    username: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    bio: { type: 'string' },
                    avatar: { type: 'string' },
                    isEmailVerified: { type: 'boolean' },
                    createdAt: { type: 'string' },
                    updatedAt: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const userId = request.currentUser.id;
    
    // Get current user profile
    const user = await userService.getCurrentUser(userId);
    
    return reply.send({
      success: true,
      data: {
        user,
      },
    });
  });

  // Update current user profile
  fastify.put('/users/me', {
    preHandler: authenticate,
    schema: {
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string', minLength: 1, maxLength: 100 },
          lastName: { type: 'string', minLength: 1, maxLength: 100 },
          bio: { type: 'string', maxLength: 1000 },
          avatar: { type: 'string', format: 'uri' },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    username: { type: 'string' },
                    email: { type: 'string' },
                    firstName: { type: 'string' },
                    lastName: { type: 'string' },
                    bio: { type: 'string' },
                    avatar: { type: 'string' },
                    isEmailVerified: { type: 'boolean' },
                    createdAt: { type: 'string' },
                    updatedAt: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const userId = request.currentUser.id;
    
    // Validate request body
    const updateData = updateUserSchema.parse(request.body);
    
    // Update user profile
    const user = await userService.updateCurrentUser(userId, updateData);
    
    return reply.send({
      success: true,
      data: {
        user,
      },
    });
  });

  // Get user statistics (optional endpoint)
  fastify.get('/users/:username/stats', {
    schema: {
      params: {
        type: 'object',
        required: ['username'],
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 50 },
        },
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                stats: {
                  type: 'object',
                  properties: {
                    postsCount: { type: 'number' },
                    joinedDate: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    const { username } = request.params;
    
    // First get user to ensure they exist
    const user = await userService.getUserByUsername(username);
    
    // Get user statistics
    const stats = await userService.getUserStats(user.id);
    
    return reply.send({
      success: true,
      data: {
        stats,
      },
    });
  });
}
