import { eq, ne } from 'drizzle-orm';
import { users } from '../../core/database/schema.js';
import { NotFoundError, ConflictError } from '../../core/utils/errors.js';

export class UserService {
  constructor(db) {
    this.db = db;
  }

  /**
   * Get user by username (public profile)
   * @param {string} username - Username to search for
   * @returns {Promise<Object>} User public profile
   */
  async getUserByUsername(username) {
    const [user] = await this.db
      .select({
        id: users.id,
        username: users.username,
        firstName: users.firstName,
        lastName: users.lastName,
        bio: users.bio,
        avatar: users.avatar,
        createdAt: users.createdAt,
      })
      .from(users)
      .where(eq(users.username, username))
      .limit(1);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  }

  /**
   * Get current user profile (private)
   * @param {string} userId - Current user ID
   * @returns {Promise<Object>} User private profile
   */
  async getCurrentUser(userId) {
    const [user] = await this.db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        firstName: users.firstName,
        lastName: users.lastName,
        bio: users.bio,
        avatar: users.avatar,
        isEmailVerified: users.isEmailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  }

  /**
   * Update current user profile
   * @param {string} userId - Current user ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated user profile
   */
  async updateCurrentUser(userId, updateData) {
    const { firstName, lastName, bio, avatar } = updateData;

    // Check if user exists
    const [existingUser] = await this.db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!existingUser) {
      throw new NotFoundError('User not found');
    }

    // Prepare update data
    const updateFields = {};
    if (firstName !== undefined) updateFields.firstName = firstName;
    if (lastName !== undefined) updateFields.lastName = lastName;
    if (bio !== undefined) updateFields.bio = bio;
    if (avatar !== undefined) updateFields.avatar = avatar;

    // Update user
    if (Object.keys(updateFields).length > 0) {
      await this.db
        .update(users)
        .set({
          ...updateFields,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId));
    }

    // Return updated user
    return this.getCurrentUser(userId);
  }

  /**
   * Check if username is available
   * @param {string} username - Username to check
   * @param {string} excludeUserId - User ID to exclude from check (for updates)
   * @returns {Promise<boolean>} True if username is available
   */
  async isUsernameAvailable(username, excludeUserId = null) {
    let query = this.db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.username, username));

    if (excludeUserId) {
      query = query.where(ne(users.id, excludeUserId));
    }

    const [existingUser] = await query.limit(1);
    return !existingUser;
  }

  /**
   * Check if email is available
   * @param {string} email - Email to check
   * @param {string} excludeUserId - User ID to exclude from check (for updates)
   * @returns {Promise<boolean>} True if email is available
   */
  async isEmailAvailable(email, excludeUserId = null) {
    let query = this.db
      .select({ id: users.id })
      .from(users)
      .where(eq(users.email, email));

    if (excludeUserId) {
      query = query.where(ne(users.id, excludeUserId));
    }

    const [existingUser] = await query.limit(1);
    return !existingUser;
  }

  /**
   * Get user statistics
   * @param {string} userId - User ID
   * @returns {Promise<Object>} User statistics
   */
  async getUserStats(userId) {
    // This would typically include post count, followers, etc.
    // For now, we'll return basic stats
    return {
      postsCount: 0, // Will be implemented when posts are added
      joinedDate: new Date(), // Will get from user.createdAt
    };
  }
}
