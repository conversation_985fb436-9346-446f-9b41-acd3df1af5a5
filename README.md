# FUS App Backend v2

REST API backend sử dụng Fastify framework với các tính năng authentication, user management, và post management.

## 🚀 Tính năng

### Auth Module
- **POST /auth/register** - Đăng ký tài khoản mới với validation email và password
- **POST /auth/login** - <PERSON><PERSON>ng nhập với username/email và password, trả về JWT token
- **POST /auth/forgot-password** - Gửi email reset password qua MailgunJS
- **POST /auth/reset-password** - Đặt lại mật khẩu với token từ email
- **POST /auth/logout** - <PERSON><PERSON><PERSON> xuất và invalidate JWT token

### User Module
- **GET /users/:username** - Xem thông tin public của user theo username
- **GET /users/me** - Lấy thông tin user hiện tại (cần authentication)
- **PUT /users/me** - <PERSON><PERSON><PERSON> nhật thông tin user hiện tại (cần authentication)

### Post Module
- **GET /posts** - <PERSON><PERSON><PERSON> danh sách tất cả bài viết với pagination và search
- **GET /posts/:id** - Xem chi tiết một bài viết
- **POST /posts** - Tạo bài viết mới (cần authentication)
- **PUT /posts/:id** - Chỉnh sửa bài viết của chính mình (cần authentication + ownership check)
- **DELETE /posts/:id** - Xóa bài viết của chính mình (cần authentication + ownership check)

## 🛠 Công nghệ sử dụng

- **Framework**: Fastify
- **Database**: MySQL với Drizzle ORM
- **Authentication**: JWT với bcrypt
- **Email**: MailgunJS
- **Validation**: Zod
- **Environment**: dotenv

## 📦 Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd fus-app-be-v2
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Tạo database MySQL và cấu hình environment:
```bash
cp .env.example .env
# Chỉnh sửa file .env với thông tin database và các cấu hình khác
```

4. Chạy database migrations:
```bash
npm run db:generate
npm run db:migrate
```

5. Khởi động server:
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 🔧 Environment Variables

Xem file `.env.example` để biết các biến môi trường cần thiết:

- `NODE_ENV` - Environment (development/production)
- `PORT` - Server port (default: 3000)
- `DB_*` - Database configuration
- `JWT_SECRET` - JWT secret key (tối thiểu 32 ký tự)
- `MAILGUN_*` - Mailgun configuration cho email
- `APP_URL` - Application URL
- `FRONTEND_URL` - Frontend URL cho CORS

## 📚 API Documentation

### Authentication

#### Register
```http
POST /auth/register
Content-Type: application/json

{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "John",
  "lastName": "Doe"
}
```

#### Login
```http
POST /auth/login
Content-Type: application/json

{
  "login": "johndoe", // username hoặc email
  "password": "SecurePass123"
}
```

#### Forgot Password
```http
POST /auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### Reset Password
```http
POST /auth/reset-password
Content-Type: application/json

{
  "token": "reset_token_from_email",
  "password": "NewSecurePass123"
}
```

### Users

#### Get User Profile (Public)
```http
GET /users/:username
```

#### Get Current User Profile
```http
GET /users/me
Authorization: Bearer <jwt_token>
```

#### Update Current User Profile
```http
PUT /users/me
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe",
  "bio": "Software Developer",
  "avatar": "https://example.com/avatar.jpg"
}
```

### Posts

#### Get All Posts
```http
GET /posts?page=1&limit=10&search=keyword
```

#### Get Post by ID
```http
GET /posts/:id
```

#### Create Post
```http
POST /posts
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "My First Post",
  "content": "This is the content of my post...",
  "excerpt": "Short description",
  "isPublished": true
}
```

#### Update Post
```http
PUT /posts/:id
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "title": "Updated Title",
  "content": "Updated content...",
  "isPublished": true
}
```

#### Delete Post
```http
DELETE /posts/:id
Authorization: Bearer <jwt_token>
```

## 🧪 Testing

```bash
# Chạy tests
npm test

# Chạy tests với watch mode
npm run test:watch
```

## 📁 Cấu trúc dự án

```
src/
├── core/
│   ├── config/          # Configuration management
│   ├── database/        # Database connection và schema
│   ├── middleware/      # Custom middleware
│   └── utils/           # Utility functions và error handling
├── modules/
│   ├── auth/           # Authentication module
│   ├── user/           # User management module
│   └── post/           # Post management module
├── services/           # External services (email, etc.)
└── app.js             # Main application file
```

## 🔒 Security Features

- JWT authentication với secure secret
- Password hashing với bcrypt (12 rounds)
- Rate limiting (100 requests/minute)
- CORS protection
- Security headers với Helmet
- Input validation với Zod schemas
- SQL injection protection với Drizzle ORM

## 🚀 Deployment

1. Set environment variables cho production
2. Build và start application:
```bash
NODE_ENV=production npm start
```

## 📝 License

MIT License
