import { mysqlTable, varchar, text, timestamp, int, boolean, index } from 'drizzle-orm/mysql-core';
import { relations } from 'drizzle-orm';

// Users table
export const users = mysqlTable('users', {
  id: varchar('id', { length: 21 }).primaryKey(), // nanoid
  username: varchar('username', { length: 50 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  firstName: varchar('first_name', { length: 100 }),
  lastName: varchar('last_name', { length: 100 }),
  bio: text('bio'),
  avatar: varchar('avatar', { length: 500 }),
  isEmailVerified: boolean('is_email_verified').default(false),
  resetPasswordToken: varchar('reset_password_token', { length: 255 }),
  resetPasswordExpires: timestamp('reset_password_expires'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  usernameIdx: index('username_idx').on(table.username),
  emailIdx: index('email_idx').on(table.email),
}));

// Posts table
export const posts = mysqlTable('posts', {
  id: varchar('id', { length: 21 }).primaryKey(), // nanoid
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  excerpt: varchar('excerpt', { length: 500 }),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  authorId: varchar('author_id', { length: 21 }).notNull(),
  isPublished: boolean('is_published').default(false),
  publishedAt: timestamp('published_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow(),
}, (table) => ({
  authorIdx: index('author_idx').on(table.authorId),
  slugIdx: index('slug_idx').on(table.slug),
  publishedIdx: index('published_idx').on(table.isPublished),
}));

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  posts: many(posts),
}));

export const postsRelations = relations(posts, ({ one }) => ({
  author: one(users, {
    fields: [posts.authorId],
    references: [users.id],
  }),
}));
