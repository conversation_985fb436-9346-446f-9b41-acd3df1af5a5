{"name": "fus-app-be-v2", "version": "1.0.0", "description": "REST API backend using Fastify framework", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["fastify", "rest-api", "nodejs", "drizzle-orm", "jwt", "authentication"], "author": "", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/rate-limit": "^9.1.0", "bcrypt": "^5.1.1", "drizzle-orm": "^0.33.0", "fastify": "^4.28.1", "mailgun.js": "^10.2.3", "mysql2": "^3.11.3", "nanoid": "^5.0.7", "zod": "^3.23.8"}, "devDependencies": {"drizzle-kit": "^0.24.2", "jest": "^29.7.0", "nodemon": "^3.1.4"}}