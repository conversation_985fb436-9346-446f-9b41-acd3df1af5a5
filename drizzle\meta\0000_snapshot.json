{"version": "5", "dialect": "mysql", "id": "a6eb5c48-bf96-4a3e-a754-488ea73600bd", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"posts": {"name": "posts", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(21)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "excerpt": {"name": "excerpt", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(21)", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {"author_idx": {"name": "author_idx", "columns": ["author_id"], "isUnique": false}, "slug_idx": {"name": "slug_idx", "columns": ["slug"], "isUnique": false}, "published_idx": {"name": "published_idx", "columns": ["is_published"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"posts_id": {"name": "posts_id", "columns": ["id"]}}, "uniqueConstraints": {"posts_slug_unique": {"name": "posts_slug_unique", "columns": ["slug"]}}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(21)", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reset_password_expires": {"name": "reset_password_expires", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {"username_idx": {"name": "username_idx", "columns": ["username"], "isUnique": false}, "email_idx": {"name": "email_idx", "columns": ["email"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}}}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}