import { nanoid } from 'nanoid';
import { eq, and, or, like, desc, count } from 'drizzle-orm';
import { posts, users } from '../../core/database/schema.js';
import { NotFoundError, AuthorizationError } from '../../core/utils/errors.js';

export class PostService {
  constructor(db) {
    this.db = db;
  }

  /**
   * Create a new post
   * @param {Object} postData - Post data
   * @param {string} authorId - Author user ID
   * @returns {Promise<Object>} Created post
   */
  async createPost(postData, authorId) {
    const { title, content, excerpt, isPublished = false } = postData;
    
    // Generate slug from title
    const slug = this.generateSlug(title);
    
    // Create post
    const postId = nanoid();
    await this.db.insert(posts).values({
      id: postId,
      title,
      content,
      excerpt: excerpt || this.generateExcerpt(content),
      slug,
      authorId,
      isPublished,
      publishedAt: isPublished ? new Date() : null,
    });

    // Return created post with author info
    return this.getPostById(postId);
  }

  /**
   * Get all posts with pagination and search
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Posts with pagination metadata
   */
  async getPosts(options = {}) {
    const { page = 1, limit = 10, search = '' } = options;
    const offset = (page - 1) * limit;

    // Build base query
    let whereConditions = eq(posts.isPublished, true);
    
    // Add search condition if provided
    if (search) {
      whereConditions = and(
        whereConditions,
        or(
          like(posts.title, `%${search}%`),
          like(posts.content, `%${search}%`)
        )
      );
    }

    // Get posts with author information
    const postsResult = await this.db
      .select({
        id: posts.id,
        title: posts.title,
        content: posts.content,
        excerpt: posts.excerpt,
        slug: posts.slug,
        isPublished: posts.isPublished,
        publishedAt: posts.publishedAt,
        createdAt: posts.createdAt,
        updatedAt: posts.updatedAt,
        author: {
          id: users.id,
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          avatar: users.avatar,
        },
      })
      .from(posts)
      .leftJoin(users, eq(posts.authorId, users.id))
      .where(whereConditions)
      .orderBy(desc(posts.publishedAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(posts)
      .where(whereConditions);

    const totalPages = Math.ceil(total / limit);

    return {
      posts: postsResult,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Get post by ID
   * @param {string} postId - Post ID
   * @returns {Promise<Object>} Post with author info
   */
  async getPostById(postId) {
    const [post] = await this.db
      .select({
        id: posts.id,
        title: posts.title,
        content: posts.content,
        excerpt: posts.excerpt,
        slug: posts.slug,
        authorId: posts.authorId,
        isPublished: posts.isPublished,
        publishedAt: posts.publishedAt,
        createdAt: posts.createdAt,
        updatedAt: posts.updatedAt,
        author: {
          id: users.id,
          username: users.username,
          firstName: users.firstName,
          lastName: users.lastName,
          avatar: users.avatar,
        },
      })
      .from(posts)
      .leftJoin(users, eq(posts.authorId, users.id))
      .where(eq(posts.id, postId))
      .limit(1);

    if (!post) {
      throw new NotFoundError('Post not found');
    }

    return post;
  }

  /**
   * Update post
   * @param {string} postId - Post ID
   * @param {Object} updateData - Data to update
   * @param {string} userId - Current user ID
   * @returns {Promise<Object>} Updated post
   */
  async updatePost(postId, updateData, userId) {
    // Check if post exists and user owns it
    const existingPost = await this.getPostById(postId);
    
    if (existingPost.authorId !== userId) {
      throw new AuthorizationError('You can only edit your own posts');
    }

    const { title, content, excerpt, isPublished } = updateData;

    // Prepare update data
    const updateFields = {};
    if (title !== undefined) {
      updateFields.title = title;
      updateFields.slug = this.generateSlug(title);
    }
    if (content !== undefined) {
      updateFields.content = content;
      updateFields.excerpt = excerpt || this.generateExcerpt(content);
    }
    if (excerpt !== undefined) updateFields.excerpt = excerpt;
    if (isPublished !== undefined) {
      updateFields.isPublished = isPublished;
      if (isPublished && !existingPost.publishedAt) {
        updateFields.publishedAt = new Date();
      }
    }

    // Update post
    if (Object.keys(updateFields).length > 0) {
      await this.db
        .update(posts)
        .set({
          ...updateFields,
          updatedAt: new Date(),
        })
        .where(eq(posts.id, postId));
    }

    // Return updated post
    return this.getPostById(postId);
  }

  /**
   * Delete post
   * @param {string} postId - Post ID
   * @param {string} userId - Current user ID
   * @returns {Promise<void>}
   */
  async deletePost(postId, userId) {
    // Check if post exists and user owns it
    const existingPost = await this.getPostById(postId);
    
    if (existingPost.authorId !== userId) {
      throw new AuthorizationError('You can only delete your own posts');
    }

    // Delete post
    await this.db.delete(posts).where(eq(posts.id, postId));
  }

  /**
   * Get posts by author
   * @param {string} authorId - Author user ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Author's posts with pagination
   */
  async getPostsByAuthor(authorId, options = {}) {
    const { page = 1, limit = 10, includeUnpublished = false } = options;
    const offset = (page - 1) * limit;

    // Build where condition
    let whereConditions = eq(posts.authorId, authorId);
    if (!includeUnpublished) {
      whereConditions = and(whereConditions, eq(posts.isPublished, true));
    }

    // Get posts
    const postsResult = await this.db
      .select({
        id: posts.id,
        title: posts.title,
        content: posts.content,
        excerpt: posts.excerpt,
        slug: posts.slug,
        isPublished: posts.isPublished,
        publishedAt: posts.publishedAt,
        createdAt: posts.createdAt,
        updatedAt: posts.updatedAt,
      })
      .from(posts)
      .where(whereConditions)
      .orderBy(desc(posts.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(posts)
      .where(whereConditions);

    const totalPages = Math.ceil(total / limit);

    return {
      posts: postsResult,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  /**
   * Generate slug from title
   * @param {string} title - Post title
   * @returns {string} Generated slug
   */
  generateSlug(title) {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-') + '-' + nanoid(8);
  }

  /**
   * Generate excerpt from content
   * @param {string} content - Post content
   * @param {number} maxLength - Maximum excerpt length
   * @returns {string} Generated excerpt
   */
  generateExcerpt(content, maxLength = 200) {
    const plainText = content.replace(/<[^>]*>/g, ''); // Remove HTML tags
    if (plainText.length <= maxLength) {
      return plainText;
    }
    return plainText.substring(0, maxLength).trim() + '...';
  }
}
